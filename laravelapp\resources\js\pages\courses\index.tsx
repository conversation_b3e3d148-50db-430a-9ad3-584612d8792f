import { Head } from '@inertiajs/react';
import { But<PERSON> } from '@/components/ui/button';
import { Link } from '@inertiajs/react';

interface Course {
    id: number;
    name: string;
    created_at: string;
}

interface CoursesProps {
    courses: Course[];
}

export default function Courses({ courses }: CoursesProps) {
    return (
        <div className="min-h-screen bg-gray-50 py-12">
            <Head title="My Courses" />

            <div className="max-w-4xl mx-auto px-4">
                <div className="bg-white rounded-lg shadow p-6">
                    <div className="flex justify-between items-center mb-6">
                        <h1 className="text-2xl font-bold text-gray-900">My Courses</h1>
                        <Link href="/courses/add">
                            <Button>Add New Course</Button>
                        </Link>
                    </div>

                    {courses.length === 0 ? (
                        <div className="text-center py-12">
                            <p className="text-gray-500 mb-4">No courses added yet.</p>
                            <Link href="/courses/add">
                                <Button>Add Your First Course</Button>
                            </Link>
                        </div>
                    ) : (
                        <div className="grid gap-4">
                            {courses.map((course) => (
                                <div key={course.id} className="border rounded-lg p-4 hover:bg-gray-50">
                                    <h3 className="font-medium text-lg">{course.name}</h3>
                                    <p className="text-sm text-gray-500">
                                        Added on {new Date(course.created_at).toLocaleDateString()}
                                    </p>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
}