import { Head } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Form } from '@inertiajs/react';
import InputError from '@/components/input-error';
import { useState } from 'react';
import { Link } from '@inertiajs/react';

export default function AddCourse() {
    const [courses, setCourses] = useState<string[]>([]);
    const [currentCourse, setCurrentCourse] = useState('');

    const addCourse = () => {
        if (currentCourse.trim() && !courses.includes(currentCourse.trim())) {
            setCourses([...courses, currentCourse.trim()]);
            setCurrentCourse('');
        }
    };

    const removeCourse = (courseToRemove: string) => {
        setCourses(courses.filter(course => course !== courseToRemove));
    };

    return (
        <div className="min-h-screen bg-gray-50 py-12">
            <Head title="Add Courses" />
            
            <div className="max-w-2xl mx-auto px-4">
                <div className="bg-white rounded-lg shadow p-6">
                    <h1 className="text-2xl font-bold text-gray-900 mb-6">Add Your Courses</h1>
                    
                    <div className="space-y-6">
                        <div className="grid gap-2">
                            <Label htmlFor="course">Course Name</Label>
                            <div className="flex gap-2">
                                <Input
                                    id="course"
                                    type="text"
                                    value={currentCourse}
                                    onChange={(e) => setCurrentCourse(e.target.value)}
                                    placeholder="Enter course name"
                                    onKeyPress={(e) => e.key === 'Enter' && addCourse()}
                                />
                                <Button onClick={addCourse} disabled={!currentCourse.trim()}>
                                    Add Course
                                </Button>
                            </div>
                        </div>

                        {courses.length > 0 && (
                            <div className="space-y-3">
                                <h3 className="text-lg font-medium">Added Courses:</h3>
                                <div className="space-y-2">
                                    {courses.map((course, index) => (
                                        <div key={index} className="flex items-center justify-between bg-gray-50 p-3 rounded">
                                            <span>{course}</span>
                                            <Button 
                                                variant="destructive" 
                                                size="sm"
                                                onClick={() => removeCourse(course)}
                                            >
                                                Remove
                                            </Button>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        <div className="flex gap-4 pt-4">
                            <Form method="post" action="/courses" className="flex-1">
                                <input type="hidden" name="courses" value={JSON.stringify(courses)} />
                                <Button type="submit" className="w-full" disabled={courses.length === 0}>
                                    Save All Courses
                                </Button>
                            </Form>
                            
                            <Link href="/courses">
                                <Button variant="outline" className="w-full">
                                    View All Courses
                                </Button>
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
}